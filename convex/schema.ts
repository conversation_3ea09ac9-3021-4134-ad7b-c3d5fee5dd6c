import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    clerkId: v.string(),
    email: v.string(),
    name: v.string(),
    xpTotal: v.number(),
    streak: v.number(),
    createdAt: v.string(),
    lastActiveAt: v.string(),
  }).index("by_clerk_id", ["clerkId"]),
  
  grades: defineTable({
    name: v.string(),
    displayOrder: v.number(),
    isActive: v.boolean(),
  }),
  
  subjects: defineTable({
    name: v.string(),
    gradeId: v.id("grades"),
    displayOrder: v.number(),
    isActive: v.boolean(),
    description: v.optional(v.string()),
  }).index("by_grade", ["gradeId"]),
  
  units: defineTable({
    name: v.string(),
    subjectId: v.id("subjects"),
    displayOrder: v.number(),
    isActive: v.boolean(),
    description: v.optional(v.string()),
  }).index("by_subject", ["subjectId"]),
  
  lessons: defineTable({
    title: v.string(),
    description: v.string(),
    videoUrl: v.optional(v.string()),
    thumbnailUrl: v.optional(v.string()),
    gradeId: v.id("grades"),
    subjectId: v.id("subjects"),
    unitId: v.id("units"),
    isPublished: v.boolean(),
    durationSeconds: v.optional(v.number()),
    displayOrder: v.number(),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_grade", ["gradeId"])
    .index("by_subject", ["subjectId"])
    .index("by_unit", ["unitId"]),
  
  lessonProgress: defineTable({
    userId: v.id("users"),
    lessonId: v.id("lessons"),
    completedAt: v.optional(v.string()),
    progressPercent: v.number(),
    lastWatchedAt: v.string(),
    xpEarned: v.number(),
  }).index("by_user_lesson", ["userId", "lessonId"])
    .index("by_user", ["userId"]),
  
  chatMessages: defineTable({
    userId: v.id("users"),
    content: v.string(),
    role: v.string(), // "user" or "assistant"
    createdAt: v.string(),
    sessionId: v.optional(v.string()),
  }).index("by_user", ["userId"])
    .index("by_session", ["sessionId"]),
  
  quizzes: defineTable({
    lessonId: v.id("lessons"),
    title: v.string(),
    questions: v.array(v.object({
      question: v.string(),
      options: v.array(v.string()),
      correctAnswer: v.number(),
      explanation: v.optional(v.string()),
    })),
    isActive: v.boolean(),
    createdAt: v.string(),
  }).index("by_lesson", ["lessonId"]),
  
  quizAttempts: defineTable({
    userId: v.id("users"),
    quizId: v.id("quizzes"),
    answers: v.array(v.number()),
    score: v.number(),
    completedAt: v.string(),
    xpEarned: v.number(),
  }).index("by_user_quiz", ["userId", "quizId"])
    .index("by_user", ["userId"]),
});
