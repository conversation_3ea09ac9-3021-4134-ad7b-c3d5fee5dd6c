API Layer Specification

Purpose: Define every client-visible endpoint or Convex function in Qeleme. Each entry lists method/path (or function name), auth rules, request & response schemas, pagination, and standardized errors.

⸻

0. Conventions
	•	Transport split
• REST (Next.js API Routes) → /api/* for third-party calls, webhooks, streaming responses.
• Convex Functions → query, mutation, internalMutation for real-time DB access.
	•	Naming
• REST paths: kebab-case (/api/ai/chat).
• Convex functions: dot.case (grades.list).
	•	Auth tags
• Public – no session.
• Protected – valid student session.
• Admin – admin role only.
• Internal – callable only from server.
	•	Pagination
Use limit (default 20) & cursor (opaque string) query params; responses include nextCursor.
	•	Standard Error Envelope

{ "error": { "code": "STRING", "message": "Human-readable message" } }

HTTP status codes mirror code (400, 401, 403, 404, 409, 429, 500).

⸻

1. Authentication

1.1 Session Check

GET /api/auth/session – Protected
Returns current user profile. Errors: 401.

1.2 Update Profile

PATCH /api/users/profile – Protected
Body { name?, grade?, language?, avatarUrl?, tutorialCompleted? }
Errors: 400.

1.3 OTP Send

POST /api/auth/otp/send – Public
Body { phone }
Errors: 400, 429 (rate-limit).

1.4 OTP Verify + Sign-in

POST /api/auth/otp/verify – Public
Body { phone, code }
Errors: 400, 404.

1.5 Sign-out

POST /api/auth/sign-out – Protected
Errors: 401.

1.6 Clerk Webhook

POST /api/webhooks/clerk – Public (signature verified)
Body = Clerk payload. Calls users.createOrUpdate (Internal Mutation).

Convex Functions

Name	Type	Purpose
users.createOrUpdate	internalMutation	Sync Clerk user → Convex
users.getCurrent	query	Return current user doc
users.update	mutation	Patch profile fields


⸻

2. Academic Content

REST (public browse)

Endpoint	Method	Auth	Notes
/api/content/grades	GET	Public	Paginated list
/api/content/grades/{gradeId}/subjects	GET	Public	
/api/content/subjects/{subjectId}/units	GET	Public	
/api/content/units/{unitId}/lessons	GET	Public	Accepts limit,cursor
/api/content/lessons/{lessonId}	GET	Protected	Returns lesson + optional progress

Convex Functions (real-time)
	•	grades.list, subjects.listByGrade, units.listBySubject, lessons.listByUnit (queries)
	•	lessons.getDetails(userId, lessonId) (query)
	•	Admin-only mutations: grades.upsert, subjects.upsert, units.upsert, lessons.upsert, lessons.delete (Admin auth).

⸻

3. Learning & Progress

Convex Functions

Name	Type	Purpose
lessonProgress.update	mutation	Save last position, mark complete
quizzes.getByLesson	query	Return quiz w/questions & choices
quizAttempts.submit	mutation	Grade answers, create QuizAttempt, emit XP
xpTransactions.add	internalMutation	Append ledger entry; update Users.xp*


⸻

4. Daily Challenge

Endpoint	Method	Auth	Purpose
/api/daily-challenge	GET	Protected	Get today’s challenge + status
/api/daily-challenge/{challengeId}/submit	POST	Protected	Body { choiceId } → returns correctness, XP, new streak

Underlying Convex: dailyChallenges.getToday, dailyChallenges.submitAnswer.

⸻

5. Gamification

Endpoint	Method	Auth	Notes
/api/gamification/xp	GET	Protected	?limit&cursor returns XP ledger
/api/gamification/achievements	GET	Protected	List unlocked achievements

Convex: xpTransactions.list, achievements.list.

⸻

6. AI Tutor Chat

REST

Endpoint	Method	Auth	Purpose
/api/ai/chat	POST (stream)	Protected	Body { chatId?, messages, attachmentUrls? } → streams assistant response; on success calls aiTutor.addXp
/api/ai/chat/{chatId}/attachments	POST	Protected	multipart file upload → returns Cloudinary URL

Convex

Name	Type	Purpose
aiTutor.getOrCreateChat	query	Fetch or create chat session
aiTutor.addXp	internalMutation	+5 XP per assistant reply
chatMessages.insert	mutation	Save user or assistant message

Rate-limit: max 20 AI calls / hour (429 on exceed).

⸻

7. File Uploads

7.1 Cloudinary Signature

POST /api/uploads/cloudinary-signature – Protected
Body { fileType, folder } → returns { timestamp, signature, apiKey, cloudName }

7.2 Mux Direct-Upload URL

POST /api/uploads/mux-url – Protected (Admin)
Body { filename, contentType } → returns { uploadUrl, assetId }

⸻

8. Payments (Future)

Endpoint	Method	Auth	Notes
/api/payments/intent	POST	Protected	Body { amount, provider } → checkout URL
/api/payments/webhook/{provider}	POST	Public (signature)	Updates Payments table

Convex: payments.recordIntent, payments.updateStatus (internal).

⸻

9. Community (Future)

Endpoint	Method	Auth	Purpose
/api/community/forums	GET	Public	List forums
/api/community/forums/{forumId}/posts	GET	Public	List posts with limit,cursor
/api/community/forums/{forumId}/posts	POST	Protected	Create post

Admin mutations exist for moderation (posts.delete, forums.create).

⸻

Error Codes Reference

HTTP	Code	Meaning
400	INVALID_REQUEST	Payload failed validation
401	UNAUTHORIZED	Missing/invalid session
403	FORBIDDEN	Authenticated but lacks role
404	NOT_FOUND	Resource does not exist
409	CONFLICT	Duplicate or out-of-date data
429	RATE_LIMIT	Too many requests
500	SERVER_ERROR	Unhandled exception



---

## Real-Time Strategy (Hybrid REST + Convex)

While REST endpoints remain the **primary contract** for all client ↔︎ server communication, certain UI elements
benefit from Convex’s built-in live queries.  
Use the following rule of thumb:

| Use Case                               | Access Method                | Rationale                                  |
|----------------------------------------|------------------------------|--------------------------------------------|
| High-frequency, live-updating data     | **Direct Convex `query`**    | Instant UI refresh without extra polling. |
| Low-frequency or cacheable data        | **REST `/api/*` route**      | Enables HTTP caching & BFF validation.     |
| Mixed interactions (e.g., AI Chat)     | **REST for writes**, Convex for reads | Writes need validation; reads need live updates. |

### Recommended Direct-Query Hooks

1. **Dashboard metrics** – `users.getCurrent` (xpTotal, xpToday, streakCount).  
2. **AI tutor chat history** – `aiTutor.getOrCreateChat`.  
3. **Live lesson progress bar** – `lessonProgress.getByUserLesson` (optional).

All other data access patterns (content hierarchy, quiz submission, daily challenge, uploads, payments) **must**
use the REST endpoints defined above to preserve caching, security, and portability.

> **Implementation note:** When a REST mutation changes data that a live Convex query subscribes to, Convex will
> automatically push updates to the client—no additional code required.

---

