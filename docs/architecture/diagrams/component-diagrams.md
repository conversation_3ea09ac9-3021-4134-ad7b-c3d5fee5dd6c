
# Component Diagrams

> **Purpose:** Provide a high-level map of Qeleme’s architecture so developers (and AI agents) quickly understand how UI pieces and backend modules fit together.

---

## 1. Frontend (Next.js 14 App Router)

```mermaid
graph TD
  A[RootLayout<br>(layout.tsx)] --> B[Public Pages<br>/(home)]
  A --> C[(auth) Route Group]
  A --> D[(protected) Route Group]

  C --> C1[/sign-in]
  C --> C2[/sign-up]
  C --> C3[/onboarding]

  D --> D1[/dashboard]
  D --> D2[/ai-tutor]
  D --> D3[/daily-challenge]
  D --> D4[/profile]

  D4 --> D4a[SettingsSheet]
  D1 --> D1a[DashboardStats]
  D1 --> D1b[GradeSelector]
  D1 --> D1c[ProgressBars]

  D2 --> D2a[ChatWindow]
  D2a --> D2b[MessageBubble]
  D2a --> D2c[Attachments]

  D --> E[/grades/[gradeId]/subjects/[subjectId]/units/[unitId]/lessons/[lessonId]]
  E --> E1[LessonPlayer]
  E1 --> E2[VideoPlayer]
  E1 --> E3[QuizModal]

  subgraph Shared Widgets
    N[TopNav] --- M[BottomNav]
    M --- P[ToastNotifications]
  end

Notes
	•	RootLayout wraps the entire app in <ClerkProvider>, <ConvexProvider>, and <Sentry.ErrorBoundary>.
	•	(auth) pages use PublicLayout — no protected data hooks.
	•	(protected) pages are automatically wrapped with RequireAuth HOC (redirects to /sign-in).
	•	LessonPlayer pauses video at quiz timestamps and opens QuizModal.
	•	TanStack Query caches REST data; Convex useQuery powers real-time XP/streak updates.

⸻

2. Backend Modules

graph LR
  subgraph Next.js API Routes
    APISession[/api/auth/session]
    APIOTP[/api/auth/otp/*]
    APIChat[/api/ai/chat]
    APIUploadMux[/api/uploads/mux-url]
    APIUploadCld[/api/uploads/cloudinary-signature]
    APIClerkWH[/api/webhooks/clerk]
    APIMuxWH[/api/webhooks/mux]
    APITwilioWH[/api/webhooks/twilio]
    APIResendWH[/api/webhooks/resend]
    APIPayIntent[/api/payments/intent]
    APIPayWH[/api/payments/webhook/:provider]
  end

  subgraph Convex Functions
    AuthFn[users.*]
    ContentFn[grades.*, subjects.*, units.*, lessons.*]
    ProgressFn[lessonProgress.*, quizAttempts.*, xpTransactions.*]
    DailyFn[dailyChallenges.*]
    AIFn[aiTutor.*, chatMessages.*]
    UploadFn[attachments.*]
    PaymentFn[payments.*]
    CommunityFn[forums.*, posts.*]
  end

  APISession --> AuthFn
  APIOTP --> AuthFn
  APIChat --> AIFn
  APIPayIntent --> PaymentFn
  APIPayWH --> PaymentFn
  APIUploadMux --> UploadFn
  APIUploadCld --> UploadFn
  APIClerkWH --> AuthFn
  APIMuxWH --> ContentFn
  APITwilioWH --> AuthFn
  APIResendWH --> AuthFn

  subgraph External Services
    Clerk[(Clerk)]
    OpenAI[(OpenAI)]
    Mux[(Mux)]
    Cloudinary[(Cloudinary)]
    Twilio[(Twilio)]
    Resend[(Resend)]
    Chopa[(Chopa)]
    Talebirr[(Talebirr)]
  end

  Clerk --> APIClerkWH
  OpenAI --> APIChat
  Mux --> APIUploadMux & APIMuxWH
  Cloudinary --> APIUploadCld
  Twilio --> APITwilioWH
  Resend --> APIResendWH
  Chopa --> APIPayWH
  Talebirr --> APIPayWH

Module Breakdown

Module	Convex Functions	API Routes	External Service
AuthModule	users.*	/api/auth/*, Clerk webhook	Clerk
ContentModule	grades.*, subjects.*, units.*, lessons.*	/api/content/*, Mux webhook	Mux
ProgressModule	lessonProgress.*, quizAttempts.*, xpTransactions.*	/api/gamification/*, /api/daily-challenge	—
AIModule	aiTutor.*, chatMessages.*, attachments.*	/api/ai/chat	OpenAI
UploadModule	attachments.*	/api/uploads/*	Mux, Cloudinary
PaymentModule	payments.*	/api/payments/*	Chopa, Talebirr
CommunityModule	forums.*, posts.*	/api/community/* (future)	—


⸻

3. Cross-Cutting Concerns

Concern	Implementation
State Management	TanStack Query (REST) + Convex useQuery for reactive components
Error Handling	Sentry integration + global <ErrorBoundary>
Localization	Middleware + Users.language field (supports en, am)
Access Control	RequireAuth() wrapper + user.role checks (admin, student, etc.)
Analytics	Plausible for page + custom events (e.g., AI usage)
Monitoring	Sentry tagged logs for all frontend/backend issues


⸻

End of Component Diagrams

