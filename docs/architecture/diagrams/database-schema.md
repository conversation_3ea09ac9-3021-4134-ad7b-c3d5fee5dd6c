# Database Schema

> **Purpose:** Define all data entities for Qeleme’s Convex backend, covering current MVP flows and future-proofed extensions (payments, community).

---

## Core Principles
- **Real-time First:** Designed for Convex live queries.  
- **Scalable Queries:** Indexed (`by_...`) fields align with common access patterns.  
- **Typed Relations:** IDs reference parent tables; naming stays consistent (`<entity>Id`).  
- **Extensible:** Extra tables (payments, community) avoid heavy migrations later.

---

## 1. Users  
Stores core profile, preferences, and gamification metrics.

| Field               | Type                           | Description                                           | Index         |
|---------------------|--------------------------------|-------------------------------------------------------|---------------|
| `_id`               | `Id`                           | Convex document ID                                    | PK            |
| `clerkUserId`       | `String`                       | External ID from Clerk                                | `by_clerk`    |
| `email`             | `String`                       | Login email                                          | `by_email`    |
| `name`              | `String`                       | Full name                                            |               |
| `role`              | `Enum('student','admin')`      | User role                                            | `by_role`     |
| `grade`             | `Number`                       | 9–12                                                 | `by_grade`    |
| `language`          | `Enum('en','am')`              | Preferred UI language                                |               |
| `avatarUrl`         | `String`                       | Profile image URL                                    |               |
| `xpTotal`           | `Number`                       | Cumulative XP                                        |               |
| `xpToday`           | `Number`                       | XP earned today (resets daily)                       |               |
| `streakCount`       | `Number`                       | Consecutive active days                              |               |
| `lastActiveAt`      | `Number`                       | Unix timestamp of last activity                      |               |
| `tutorialCompleted` | `Boolean`                      | Finished pre-signup tutorial?                        |               |
| `createdAt`         | `Number`                       | Creation timestamp                                   |               |

---

## 2. Academic Content Hierarchy

### 2.1 Grades  
| Field         | Type      | Description       | Index |
|---------------|-----------|-------------------|-------|
| `_id`         | `Id`      | Grade ID          | PK    |
| `name`        | `String`  | “Grade 9”         |       |
| `description` | `String`  | Overview text     |       |

### 2.2 Subjects  
| Field      | Type     | Description                        | Index        |
|------------|----------|------------------------------------|--------------|
| `_id`      | `Id`     | Subject ID                         | PK           |
| `gradeId`  | `Id`     | FK → Grades                        | `by_grade`   |
| `name`     | `String` | “Physics”                          |              |
| `imageUrl` | `String` | Banner image                       |              |

### 2.3 Units  
| Field     | Type     | Description                         | Index               |
|-----------|----------|-------------------------------------|---------------------|
| `_id`     | `Id`     | Unit ID                             | PK                  |
| `subjectId`| `Id`    | FK → Subjects                       | `by_subject`        |
| `name`    | `String` | “Kinematics”                        |                     |
| `order`   | `Number` | Sequence within subject             | `by_subject_order`  |

### 2.4 Lessons  
| Field        | Type     | Description                              | Index           |
|--------------|----------|------------------------------------------|-----------------|
| `_id`        | `Id`     | Lesson ID                                | PK              |
| `unitId`     | `Id`     | FK → Units                               | `by_unit`       |
| `title`      | `String` | Lesson title                             |                 |
| `videoUrl`   | `String` | Mux playback URL                         |                 |
| `thumbnailUrl`| `String`| Cover image                              |                 |
| `duration`   | `Number` | Seconds                                  |                 |
| `notes`      | `String` | AI-generated summary                     |                 |
| `order`      | `Number` | Sequence within unit                     | `by_unit_order` |

---

## 3. Learning Activity & Progress

### 3.1 LessonProgress  
Tracks lesson completion and resume position.

| Field           | Type     | Description                              | Index               |
|-----------------|----------|------------------------------------------|---------------------|
| `_id`           | `Id`     | Progress ID                              | PK                  |
| `userId`        | `Id`     | FK → Users                               | `by_user_lesson`    |
| `lessonId`      | `Id`     | FK → Lessons                             | `by_user_lesson`    |
| `completedAt`   | `Number?`| Timestamp when finished                  |                     |
| `lastPosition`  | `Number` | Seconds into video                       |                     |

### 3.2 QuizAttempt  
| Field         | Type     | Description                              | Index            |
|---------------|----------|------------------------------------------|------------------|
| `_id`         | `Id`     | Attempt ID                               | PK               |
| `userId`      | `Id`     | FK → Users                               | `by_user_quiz`   |
| `quizId`      | `Id`     | FK → Quizzes                             | `by_user_quiz`   |
| `score`       | `Number` | Correct answers count                    |                  |
| `xpEarned`    | `Number` | XP for this quiz                         |                  |
| `attemptedAt` | `Number` | Timestamp                                |                  |

### 3.3 UserAnswer  
| Field            | Type       | Description                                      | Index        |
|------------------|------------|--------------------------------------------------|--------------|
| `_id`            | `Id`       | Answer ID                                        | PK           |
| `attemptId`      | `Id`       | FK → QuizAttempt                                 | `by_attempt` |
| `questionId`     | `Id`       | FK → Questions                                   |              |
| `selectedChoiceId`| `Id?`     | FK → Choices                                     |              |
| `answerText`     | `String?`  | For short answer                                 |              |
| `isCorrect`      | `Boolean`  | Correct?                                         |              |

### 3.4 DailyChallengeParticipation  
| Field      | Type     | Description                               | Index            |
|------------|----------|-------------------------------------------|------------------|
| `_id`      | `Id`     | Participation ID                          | PK               |
| `userId`   | `Id`     | FK → Users                                | `by_user_date`   |
| `challengeId`| `Id`   | FK → DailyChallenges                      |                  |
| `date`     | `String` | YYYY-MM-DD                                | `by_user_date`   |
| `isCorrect`| `Boolean`| Answered correctly?                       |                  |
| `xpEarned` | `Number` | XP gained                                 |                  |

### 3.5 XPTransaction  
Immutable ledger of XP events.

| Field      | Type                                 | Description                            | Index      |
|------------|--------------------------------------|----------------------------------------|------------|
| `_id`      | `Id`                                 | Transaction ID                         | PK         |
| `userId`   | `Id`                                 | FK → Users                             | `by_user`  |
| `amount`   | `Number`                             | XP delta                               |            |
| `source`   | `Enum('quiz','ai','daily','admin')`  | Origin of XP                           |            |
| `createdAt`| `Number`                             | Timestamp                              |            |

### 3.6 Achievement  
| Field            | Type       | Description                              | Index     |
|------------------|------------|------------------------------------------|-----------|
| `_id`            | `Id`       | Achievement ID                           | PK        |
| `userId`         | `Id`       | FK → Users                               | `by_user` |
| `key`            | `String`   | e.g., `quiz_master`                      |           |
| `unlockedAt`     | `Number`   | Timestamp                                |           |

---

## 4. Content & Assessment Entities

### 4.1 Quizzes  
| Field      | Type     | Description                      | Index        |
|------------|----------|----------------------------------|--------------|
| `_id`      | `Id`     | Quiz ID                          | PK           |
| `lessonId` | `Id`     | FK → Lessons                     | `by_lesson`  |
| `title`    | `String` | Quiz name                        |              |

### 4.2 Questions  
| Field      | Type                     | Description                                   | Index       |
|------------|--------------------------|-----------------------------------------------|-------------|
| `_id`      | `Id`                     | Question ID                                   | PK          |
| `quizId`   | `Id?`                    | FK → Quizzes (null for daily)                | `by_quiz`   |
| `text`     | `String`                 | Question text                                |             |
| `type`     | `Enum('MCQ','SHORT')`    | MCQ or short answer                          |             |

### 4.3 Choices  
| Field        | Type     | Description                        | Index            |
|--------------|----------|------------------------------------|------------------|
| `_id`        | `Id`     | Choice ID                          | PK               |
| `questionId` | `Id`     | FK → Questions                     | `by_question`    |
| `text`       | `String` | Choice text                        |                  |
| `isCorrect`  | `Boolean`| Correct answer?                    |                  |

### 4.4 DailyChallenges  
| Field        | Type     | Description                        | Index       |
|--------------|----------|------------------------------------|-------------|
| `_id`        | `Id`     | Challenge ID                       | PK          |
| `date`       | `String` | YYYY-MM-DD                         | `by_date`   |
| `questionId` | `Id`     | FK → Questions                     |             |
| `title`      | `String` | Challenge title                    |             |

---

## 5. AI Tutor

### 5.1 AiTutorChats  
| Field      | Type     | Description                               | Index      |
|------------|----------|-------------------------------------------|------------|
| `_id`      | `Id`     | Chat session ID                           | PK         |
| `userId`   | `Id`     | FK → Users                               | `by_user`  |
| `createdAt`| `Number` | Session start time                        |            |

### 5.2 ChatMessages  
| Field      | Type                     | Description                                   | Index     |
|------------|--------------------------|-----------------------------------------------|-----------|
| `_id`      | `Id`                     | Message ID                                    | PK        |
| `chatId`   | `Id`                     | FK → AiTutorChats                             | `by_chat` |
| `role`     | `Enum('user','assistant')` | Message author                              |           |
| `content`  | `String`                 | Text content                                  |           |
| `createdAt`| `Number`                 | Timestamp                                     |           |

### 5.3 Attachments  
| Field       | Type     | Description                               | Index         |
|-------------|----------|-------------------------------------------|---------------|
| `_id`       | `Id`     | Attachment ID                              | PK            |
| `messageId` | `Id`     | FK → ChatMessages                          | `by_message`  |
| `fileUrl`   | `String` | Cloudinary URL                             |               |
| `fileType`  | `Enum('PDF','IMAGE')` | Attachment type                  |               |

### 5.4 MessageFeedback (Optional)  
| Field      | Type      | Description                                 | Index      |
|------------|-----------|---------------------------------------------|------------|
| `_id`      | `Id`      | Feedback ID                                 | PK         |
| `messageId`| `Id`      | FK → ChatMessages                           | `by_message`|
| `userId`   | `Id`      | FK → Users                                  |            |
| `isHelpful`| `Boolean` | Helpful?                                    |            |
| `createdAt`| `Number`  | Timestamp                                   |            |

---

## 6. Payments (Future)

### 6.1 Payments  
| Field      | Type                                   | Description                  | Index        |
|------------|----------------------------------------|------------------------------|--------------|
| `_id`      | `Id`                                   | Payment ID                   | PK           |
| `userId`   | `Id`                                   | FK → Users                   | `by_user`    |
| `amount`   | `Number`                               | ETB cents                    |              |
| `status`   | `Enum('pending','success','failed')`   | Payment state                |              |
| `provider` | `Enum('chopa','talebirr')`            | Gateway                      |              |
| `createdAt`| `Number`                               | Timestamp                    |              |

---

## 7. Community (Coming Soon)

### 7.1 Forums  
| Field      | Type     | Description           | Index |
|------------|----------|-----------------------|-------|
| `_id`      | `Id`     | Forum ID              | PK    |
| `name`     | `String` | Forum title           |       |
| `createdAt`| `Number` | Timestamp             |       |

### 7.2 Posts  
| Field      | Type     | Description              | Index        |
|------------|----------|--------------------------|--------------|
| `_id`      | `Id`     | Post ID                  | PK           |
| `forumId`  | `Id`     | FK → Forums              | `by_forum`   |
| `userId`   | `Id`     | FK → Users               | `by_forum`   |
| `content`  | `String` | Post body                |              |
| `createdAt`| `Number` | Timestamp                |              |