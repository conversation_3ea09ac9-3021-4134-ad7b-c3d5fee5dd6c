# Integration Points (v2 — Production-Ready ★★★★★)

> **Purpose:** Capture every external dependency Qeleme touches, including **how** we integrate, **where** the code lives, **security hardening**, **observability hooks**, and **compliance tasks**.  
> **Secrets** ➜ Store only in `.env.local` (dev) or Vercel → Settings → Environment Variables. Never commit secrets to Git. Rotate 🔑 every 90 days.

---

## 1 · Integration Matrix (Quick Reference)

| #  | Service                       | Integration Type                                    | Env Vars (🔑 = secret)                                              | Primary Code Location(s)                                                                                                       | Notes                                                                                                           |
|----|-------------------------------|-----------------------------------------------------|---------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------|
| 1  | **Clerk** (Auth)              | `@clerk/nextjs` SDK<br>Webhook (HMAC SHA256)        | `CLERK_PUBLISHABLE_KEY`<br>`CLERK_SECRET` 🔑                       | `src/app/layout.tsx` – `<ClerkProvider>`<br>`src/middleware.ts` – `clerkMiddleware()`<br>`pages/api/webhooks/clerk.ts`     | Webhook validates `Clerk-Signature`; retries ≤ 5 handled by Clerk.                                              |
| 2  | **Convex** (Realtime DB)      | NPM `convex@latest`                                 | `CONVEX_URL`<br>`CONVEX_WS_URL`                                     | `src/lib/convex.ts`<br>`convex/` (queries & mutations)                                                                      | Use `useQuery` for dashboard/AI chat; write mutations rate-limited (10 writes/5 s).                             |
| 3  | **Mux** (Video)               | REST + Signed URLs + **Webhook**                    | `MUX_TOKEN_ID` 🔑<br>`MUX_TOKEN_SECRET` 🔑                          | `lib/mux.ts`<br>`pages/api/uploads/mux-url.ts`<br>`pages/api/webhooks/mux.ts`                                                | Upload URL (24 h). Webhook `video.asset.ready` → mark lesson playable; log playback errors to Sentry.         |
| 4  | **Cloudinary** (Images/PDF)   | Signed Uploads + URL Transforms                     | `CLOUDINARY_API_KEY`<br>`CLOUDINARY_API_SECRET` 🔑<br>`CLOUDINARY_CLOUD_NAME` | `pages/api/uploads/cloudinary-signature.ts`                                                                                    | Limit ≤ 10 MB per file; TTL = 1 h; transform params `w_640,q_auto,f_auto`. Metrics via Admin API → Sentry.       |
| 5  | **OpenAI GPT-4o**             | `openai` SDK (Edge)                                 | `OPENAI_API_KEY` 🔑                                                  | `lib/openai.ts`<br>`pages/api/ai/chat.ts`                                                                                    | Stream responses via SSE; 20 req/h rate-limit. Rotate key monthly; enforce usage caps via dashboard.           |
| 6  | **Twilio** (SMS)              | `twilio` SDK + **Status Webhook**                   | `TWILIO_ACCOUNT_SID` 🔑<br>`TWILIO_AUTH_TOKEN` 🔑<br>`TWILIO_FROM_NUMBER` | `lib/twilio.ts`<br>`pages/api/auth/otp/send.ts`<br>`pages/api/webhooks/twilio.ts`                                           | Sender pool “QELEME”. Max 3 OTP SMS/h. Status callback logs delivery & bounce.                                 |
| 7  | **Resend** (Email)            | `@resend/client`                                    | `RESEND_API_KEY` 🔑                                                  | `lib/email.ts`<br>`pages/api/webhooks/resend.ts`                                                                            | Templates in `emails/`; webhook `email.bounced` → flag user.                                                   |
| 8  | **Plausible** (Analytics)     | JS snippet + API                                    | `PLAUSIBLE_DOMAIN`                                                   | `src/app/layout.tsx`                                                                                                         | Exclude dev via `data-exclude="localhost"`; data retention 24 months.                                           |
| 9  | **Sentry** (Monitoring)       | `@sentry/nextjs`                                     | `SENTRY_DSN` 🔑                                                      | `sentry.client.ts`<br>`sentry.server.ts`                                                                                     | Source maps uploaded in CI; performance sample 0.2 in prod.                                                    |
| 10 | **Vercel** (CI/CD)            | GitHub → Vercel hook                                 | —                                                                     | Vercel dashboard                                                                                                             | Prod = `main`, Preview = PR branches; Edge Functions memory 512 MB.                                            |
| 11 | **Chopa** (Payments)          | REST API + Webhook (HMAC)                           | `CHOPA_PUBLIC_KEY`<br>`CHOPA_SECRET_KEY` 🔑                          | `pages/api/payments/intent.ts`<br>`pages/api/payments/webhook/chopa.ts`                                                      | Idempotency key = `paymentId`; webhook retries 3×.                                                           |
| 12 | **Talebirr** (Payments)       | REST API + Webhook                                  | `TALEBIRR_KEY` 🔑                                                    | `pages/api/payments/intent.ts`<br>`pages/api/payments/webhook/talebirr.ts`                                                   | Same contract as Chopa for parity.                                                                            |
| 13 | **GitHub Actions** (CI)       | Git + Actions CI                                    | `GH_PAT` 🔑 (Actions)                                                | `.github/workflows/ci.yml`                                                                                                   | Lint → tests → typecheck → build; artifacts retained 14 days.                                                  |

---

## 2 · Detailed Integration Notes

### 2.1 Clerk
- **SDK Init:** `import { ClerkProvider } from '@clerk/nextjs'` in `src/app/layout.tsx`.  
- **Middleware:** `src/middleware.ts` uses `clerkMiddleware()` to protect routes.  
- **Webhook Security:** Verify `Clerk-Signature` header with HMAC SHA256.  
- **Sync Flow:** Webhook → `users.createOrUpdate` (internal) → update Convex Users collection.

### 2.2 Mux
- **Upload:** Client requests `/api/uploads/mux-url` → server signs direct upload URL (24 h) → client PUT.  
- **Playback:** Use signed playback URLs (exp = 7 days) via `lib/mux.ts`.  
- **Webhook:** Handle `video.asset.ready` in `/api/webhooks/mux.ts` → set `Lessons.isReady = true`.  
- **Metrics:** Daily fetch from Mux Data API → log errors to Sentry.

### 2.3 Cloudinary
- **Signature:** `/api/uploads/cloudinary-signature` → `{ timestamp, signature, publicId }`.  
- **Transformation:** Client appends `w_640,q_auto,f_auto` params.  
- **Limits:** Max file size 10 MB; TTL 1 h.  
- **Metrics:** Weekly CRON at `/api/admin/cloudinary-metrics` → send stats to Sentry.

### 2.4 OpenAI GPT-4o
- **Wrapper:** `lib/openai.ts` centralizes model and params.  
- **Streaming:** Use Node 18 `Fetch` with `streaming=true` → SSE to client.  
- **Observability:** Log prompt and token usage to Sentry and Plausible.

### 2.5 Twilio
- **OTP Flow:** `/api/auth/otp/send` generates 6-digit code in Convex `OtpCodes` (TTL = 10 min).  
- **Rate-limit:** Convex mutation enforces ≤ 3 sends/day per phone.  
- **Status Webhook:** `/api/webhooks/twilio.ts` logs delivery and bounce events to `SmsDelivery`.

### 2.6 Resend
- **Templates:** JSX in `emails/`; compiled at build time.  
- **Bounce Handling:** `/api/webhooks/resend.ts` sets `Users.emailStatus = 'bounced'`.  
- **Future:** Use batch send for newsletters.

### 2.7 Plausible
- **Script:** `<script defer data-domain="qeleme.com" src="https://plausible.io/js/plausible.js"></script>` only in prod.  
- **Dev Exclusion:** `data-exclude="localhost"`.

### 2.8 Sentry
- **Init:** `sentry.client.ts` & `sentry.server.ts`.  
- **Release Tracking:** Set `SENTRY_RELEASE` to Git SHA; upload source maps in CI.

### 2.9 Payments
- **Intent:** `/api/payments/intent` POST `{ amount, provider }` → checkout URL.  
- **Webhooks:** `/api/payments/webhook/:provider` verify HMAC → update `Payments.status`.  
- **Idempotency:** Use `paymentId` to dedupe.

### 2.10 GitHub Actions
- **Workflow:** `.github/workflows/ci.yml` runs `npm ci`, `npm run lint`, `npm test`, `npm run build`.  
- **Secrets:** Inject `GH_PAT`, `SENTRY_DSN`, etc., via GitHub Secrets → Vercel.

---

## 3 · Security & Compliance Checklist
- **Rotate secrets** every 90 days & revoke unused keys.  
- **Least-privilege:** Scope tokens to minimal permissions.  
- **Access logs:** Review monthly; Sentry alerts on anomalies.  
- **Webhook retries & idempotency:** Ensure safe processing.  
- **CORS:** Allow only `https://qeleme.com` & `https://*.vercel.app` (GET, POST, OPTIONS).  
- **GDPR:** Provide `/api/gdpr/export` and `/api/gdpr/delete`.  
- **2FA:** Enforce on all third-party dashboards (Clerk, Mux, Cloudinary, Twilio, Vercel).

---

*End of Integration Points v2*