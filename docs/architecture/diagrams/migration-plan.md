# Migration Plan

> **Purpose:** Define a repeatable, version-controlled process for evolving Qeleme’s Convex schema (and related assets) without data loss.  
> **Applies to:** Convex collections, field additions/removals, index changes, and any data backfills required by new product features.

---

## 1 · Migration Tooling

| Tool               | Version    | Command                                             |
|--------------------|------------|-----------------------------------------------------|
| **Convex CLI**     | `>= 0.21.0`| `npx convex dev`, `npx convex db migrate <file>`    |
| **GitHub Actions** | Latest     | CI job `apply-migrations.yml` (runs on `main`)      |
| **Vercel Deploy**  | —          | Production deploy blocks until migrations succeed   |

---

## 2 · Naming Conventions

convex/migrations/YYYYMMDD_<short_slug>.js

- Use UTC date stamp + a concise slug (e.g., `20250701_add_community_tables.js`).  
- Export **two** async functions:

  ```js
  export async function up(db, ctx) { /* apply change */ }
  export async function down(db, ctx) { /* rollback change */ }

	•	Idempotent: migrations must be safe to re-run and should never throw if already applied.

⸻

3 · Version Table

Ver	Date (UTC)	Slug	Description
v1	2025-06-29	20250629_mvp_baseline.js	Initial MVP schema (Users, Content hierarchy, Progress, XP ledger, AI chat, Payments, Community placeholders).
v2	Planned	20250715_add_lesson_difficulty.js	Adds difficulty field to Lessons (enum: easy, med, hard).
v3	Planned	20250801_community_enabled.js	Creates indexes on Forums & Posts; sets Community.enabled=true.
v4	Planned	20250910_payments_refund_status.js	Adds refundStatus field to Payments and backfills pending where null.
v5	Planned	20251005_message_feedback.js	Creates MessageFeedback table and indexes.

Note: Dates and versions after v1 are estimates—update before merging.

⸻

4 · Baseline Schema (v1)

20250629_mvp_baseline.js
	•	Creates all collections and indexes defined in database-schema.md.
	•	Seeds Grades (9-12) and sample Subjects if --seed flag is passed.

export async function up(db) {
  await db.createTable("Users", { /* …fields… */ });
  await db.createIndex("Users", "by_email", { email: "asc" });
  // …repeat for every table & index…
  if (process.argv.includes("--seed")) {
    await db.insert("Grades", { name: "Grade 9", description: "…" });
  }
}

export async function down(db) {
  // destructive rollback NOT recommended in prod
  console.warn("v1 rollback skipped — baseline schema");
}


⸻

5 · Applying Migrations

Local Dev

# Preview pending migrations
npx convex db migrate --plan

# Apply up migrations
npx convex db migrate

CI / GitHub Actions

steps:
  - name: Install deps & apply migrations
    run: |
      npm ci
      npx convex db migrate --yes

	•	CI fails if any migration throws; deploy blocked until fixed.

Production (Vercel)
	1.	Vercel Build Step fetches code.
	2.	Pre-deploy Hook runs npx convex db migrate --yes.
	3.	If successful, deployment continues; if not, deploy is cancelled.

⸻

6 · Rollback Strategy
	•	Single Step Revert:

npx convex db rollback <slug>


	•	Multi-Step Revert: Run rollback in reverse order until desired version.
	•	Data Backups: Nightly export of Convex data to S3 (convex export --to s3://qeleme-backups/%DATE%).

⸻

7 · Verification Checklist (Post-Migration)
	1.	Schema Diff: convex schema:diff shows expected changes only.
	2.	Smoke Tests:
	•	Sign-in / sign-out
	•	Fetch dashboard metrics
	•	Play a lesson video
	•	Submit a quiz & verify XP
	3.	Sentry: No new schema-related errors for 30 min.
	4.	Plausible: Traffic unaffected (baseline pageviews).
	5.	Rollback Plan ready if errors spike.

⸻

8 · Future Considerations
	•	Blue/Green Deploy: For large, breaking migrations, direct traffic to a staging branch until verification passes.
	•	Data Transform Scripts: For heavy backfills (e.g., XP recalculation), run in batches using Convex cron jobs.
	•	Automatic Docs Update: Git pre-commit hook checks that any migration touching schema also updates database-schema.md.

⸻

End of Migration Plan

