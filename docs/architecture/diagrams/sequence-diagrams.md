# Sequence Diagrams (v2 — Complete)

> **Purpose:** Visualize key Qeleme user flows—happy-path and key error branches—to aid testing, monitoring, and onboarding.

---

## Legend

| Symbol / Keyword | Meaning |
|------------------|---------|
| `->>`            | Synchronous request/response |
| `-->>`           | Return/response |
| `-->` / `->`     | Async fire-and-forget |
| `loop … end`    | Repeating user interaction |
| `alt … else … end` | Conditional branches (happy vs. error) |

---

## 1. User Onboarding & Signup Tutorial

```mermaid
sequenceDiagram
    participant U as User (Browser)
    participant FE as Frontend
    participant Auth as Clerk
    participant BE as Convex Functions

    U->>FE: Click “Sign In” / “Get Started”
    FE->>FE: Load tutorial slides (3–5)
    loop Slide navigation
        U->>FE: Next / Skip
    end
    FE->>Auth: Redirect to Clerk SignUp
    Auth-->>U: Show SignUp UI

    alt Valid email / Google / OTP
        U->>Auth: Submit credentials + grade
        Auth-->>BE: users.createOrUpdate()
        Auth-->>U: Session token
        U->>BE: users.getCurrent()
        BE-->>U: User profile
        U->>FE: Redirect to /onboarding or /dashboard
    else Invalid OTP
        U->>Auth: Submit wrong OTP
        Auth-->>U: Error banner “Invalid code”
    end


⸻

2. AI Tutor Chat Interaction (with Rate-Limit Branch)

sequenceDiagram
    participant U as User (Browser)
    participant FE as ChatWindow
    participant API as /api/ai/chat
    participant OpenAI as GPT-4o
    participant BE as Convex (aiTutor.*, chatMessages.*, xpTransactions.*)

    U->>FE: Enter message + attachment
    FE->>BE: aiTutor.getOrCreateChat()
    BE-->>FE: Chat session & history
    FE->>API: POST chat request

    alt Under hourly limit
        API->>OpenAI: Stream request
        OpenAI-->>API: SSE chunks
        API-->>FE: SSE response
        FE->>BE: chatMessages.insert()
        API->>BE: xpTransactions.add(+5)
        BE-->>FE: Updated XP via useQuery
    else Exceeded limit
        API-->>FE: 429 “Rate limit exceeded”
        FE->>U: Show snackbar error
    end


⸻

3. Lesson Progress & Quiz Submission

sequenceDiagram
    participant U as User
    participant LP as LessonPlayer
    participant APIAuth as /api/auth/session
    participant ProgressFn as lessonProgress.*
    participant QuizFn as quizzes.getByLesson
    participant SubmitFn as quizAttempts.submit
    participant XP as xpTransactions.add

    U->>APIAuth: GET session
    APIAuth-->>LP: xpTotal, xpToday, streak
    U->>LP: Play video
    LP->>ProgressFn: update(lastPosition)

    LP->>QuizFn: get quiz by lessonId
    QuizFn-->>LP: Quiz data
    U->>LP: Submit quiz answers
    LP->>SubmitFn: submit(answers)
    SubmitFn-->>XP: add XP (+10 per correct)
    XP-->>ProgressFn: XP ledger updated
    ProgressFn-->>LP: New xpTotal via real-time query
    SubmitFn-->>LP: score, xpEarned
    LP->>U: Show results


⸻

4. Daily Challenge Flow

sequenceDiagram
    participant U as User
    participant FE as DailyChallengePage
    participant API as /api/daily-challenge
    participant DCFn as dailyChallenges.*

    U->>API: GET /api/daily-challenge
    API-->>U: Today’s question + participation status
    U->>FE: Select answer
    FE->>API: POST /api/daily-challenge/{id}/submit

    alt Correct answer
        API-->>U: {isCorrect: true, xpEarned: 20, newStreak: 6}
    else Incorrect answer
        API-->>U: {isCorrect: false, xpEarned: 5, newStreak: 0}
    end


⸻

5. File Upload (Cloudinary Attachment for AI Chat)

sequenceDiagram
    participant U as User
    participant FE as ChatWindow
    participant SigAPI as /api/uploads/cloudinary-signature
    participant Cloud as Cloudinary

    U->>FE: Select PDF/image
    FE->>SigAPI: Request signed upload
    SigAPI-->>FE: { timestamp, signature, apiKey, cloudName }
    FE->>Cloud: POST file (signed)
    Cloud-->>FE: secure_url
    FE->>FE: Attach URL to chat message


⸻

6. Payment Intent & Webhook

sequenceDiagram
    participant U as User
    participant FE as CheckoutButton
    participant IntentAPI as /api/payments/intent
    participant Provider as Chopa / Talebirr
    participant Webhook as /api/payments/webhook/:provider
    participant BE as payments.*

    U->>IntentAPI: POST {amount, provider}
    IntentAPI-->>U: checkoutUrl
    U->>Provider: Complete payment

    Provider-->>Webhook: POST payment_status (HMAC)
    Webhook->>BE: payments.updateStatus()
    BE-->>U: Convex subscription pushes “Payment Successful” toast


⸻

7. Admin Content Upload (Lesson Video)

sequenceDiagram
    participant Admin as Admin (Browser)
    participant FE as AdminDashboard
    participant SigAPI as /api/uploads/mux-url
    participant Mux as Mux
    participant BE as lessons.upsert
    participant WH as /api/webhooks/mux

    Admin->>SigAPI: Request signed upload URL
    SigAPI-->>Admin: {uploadUrl, assetId}
    Admin->>Mux: PUT video file
    Admin->>BE: lessons.upsert({assetId, isReady:false})

    Mux-->>WH: video.asset.ready
    WH->>BE: lessons.update({isReady:true, playbackId})
    BE-->>FE: Real-time flag triggers “Lesson Live” notification


⸻

End of Sequence Diagrams v2

