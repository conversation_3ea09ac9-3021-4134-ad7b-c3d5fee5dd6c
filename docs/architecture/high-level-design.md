# High-Level Design (HLD) — v2 ★★★★★

> **Purpose:** Give any developer, PM, or AI agent a five-minute overview of how Qeleme works—from a browser click to a Convex write—plus external services and deployment topology.

---

## 0 · Legend (Diagram Notation)

| Symbol / Arrow | Meaning                              |
|----------------|--------------------------------------|
| `->>`          | Synchronous request / immediate resp |
| `-->`          | Async call (fire-and-forget / webhook) |
| `-->>`         | Response / return message            |
| `loop … end`  | Repeating interaction                |
| `alt … else … end` | Conditional branch               |

---

## 1 · Introduction & Objectives
Qeleme is an AI-powered learning platform for Ethiopian high-school students (Grades 9-12).  
This HLD shows **who** interacts with the system, **how** internal layers are organised, **where** third-party services fit, and **how** everything is deployed.  
It guides architecture decisions, onboarding, monitoring, and scaling.

---

## 2 · System Context Diagram

```mermaid
graph TB
  subgraph External Services
    Clerk[Clerk<br>(Auth)]
    Mux[Mux<br>(Video)]
    Cloud[Cloudinary<br>(Images/PDF)]
    OpenAI[OpenAI<br>(GPT-4o)]
    Twilio[Twilio<br>(SMS)]
    Resend[Resend<br>(Email)]
    Plausible[Plausible<br>(Analytics)]
    Sentry[Sentry<br>(Monitoring)]
    Chopa[Chopa<br>(Payments)]
    Talebirr[Talebirr<br>(Payments)]
  end

  Student[Student<br>(Browser)]
  Admin[Admin<br>(Browser)]
  Qeleme[(Qeleme<br>Platform)]

  Student -- REST / WebSockets --> Qeleme
  Admin -- REST / WebSockets --> Qeleme

  Qeleme -- Webhooks / SDK --> Clerk
  Qeleme -- API --> Mux
  Qeleme -- Signed URL --> Cloud
  Qeleme -- SSE / API --> OpenAI
  Qeleme -- SDK --> Twilio
  Qeleme -- SDK --> Resend
  Qeleme -- Script/API --> Plausible
  Qeleme -- SDK/API --> Sentry
  Qeleme -- API/Webhook --> Chopa
  Qeleme -- API/Webhook --> Talebirr


⸻

3 · Logical Architecture (Layered View)

graph LR
  A[Presentation<br>Next.js 14<br>React 19] --> B[BFF / API Layer<br>Edge Functions]
  B --> C[Domain Logic<br>Convex Queries + Mutations]
  C --> D[Data & Integrations<br>Convex DB + Third-Party SDKs]

  style A fill:#E0F7FA,stroke:#0277BD
  style B fill:#E8F5E9,stroke:#2E7D32
  style C fill:#FFF3E0,stroke:#EF6C00
  style D fill:#F3E5F5,stroke:#6A1B9A

Layer Descriptions

Layer	Responsibilities	Key Tech
Presentation	Pages, components, Tailwind/shadcn, TanStack Query, Convex useQuery	Next.js App Router
BFF / API	Route validation, auth checks, rate-limit, third-party proxy, webhooks	Vercel Edge Functions, Next.js API routes
Domain Logic	Business rules (XP calc, streaks), data normalization, feature flags	Convex functions (query / mutation / internal)
Data & Integrations	Convex collections, indexes, external SDK calls (Mux, Cloudinary…)	Convex Cloud + SDKs, signed URLs, webhooks


⸻

4 · Core Data Flow (Lesson + Quiz Happy Path)

sequenceDiagram
    participant Browser
    participant API as Edge API
    participant Convex as Convex Functions
    participant Mux as Mux (Video)

    Browser->>API: GET /api/content/.../lessons
    API->>Convex: lessons.listByUnit
    Convex-->>API: Lesson list
    API-->>Browser: JSON

    Browser->>Mux: GET signed video URL (stream)
    Browser->>Convex: lessonProgress.update(lastPosition)

    Browser->>API: GET /api/quizzes?lessonId=123
    API->>Convex: quizzes.getByLesson
    Convex-->>API: Quiz JSON
    API-->>Browser: Quiz data

    Browser->>API: POST /api/quizzes/submit (answers)
    API->>Convex: quizAttempts.submit
    alt Success
        Convex-->>API: {score, xpEarned}
        API-->>Browser: Show results
        Convex-->>Browser: xpTotal update via real-time query
    else Validation error
        Convex-->>API: 400 error
        API-->>Browser: Display error banner
    end

Other flows (AI chat, Daily Challenge, Payments) are documented in sequence-diagrams.md; see that file for detailed step-by-steps.

⸻

5 · Deployment Topology

graph TD
  GH[GitHub<br>Repo + Actions] --> CI[CI Job<br>lint · test · build]
  CI -- deploy --> Vercel[Vercel<br>Edge / CDN]
  Vercel --> Edge[Edge Functions<br>/api/*]
  Edge --> Convex[Convex Cloud<br>Realtime DB]
  Edge --> Third[3rd-Party APIs<br>(Mux · OpenAI · ...)]
  Third --> Webhook[Webhook Endpoints<br>(Edge)]
  Browser[User Browser] -->|HTTPS| Vercel
  Browser <-->|WebSocket| Convex

Environment	Domain(s)	Edge Memory	Notes
Preview	*.vercel.app	256 MB	Every PR auto-deploys
Prod	qeleme.com	512 MB	Protected by CORS headers


⸻

6 · Non-Functional Considerations

Area	Approach
Scalability	Stateless Edge Functions, horizontal Convex shards, signed URLs offload media to Mux/Cloudinary.
Reliability	Webhook retries & idempotency, Blue/Green deploy on major migrations, nightly Convex export to S3 bucket.
Security	Clerk JWT auth, HMAC-verified webhooks, strict CORS, 2FA on dashboards, 90-day secret rotation.
Performance	Core Web Vitals budget: LCP < 2.5 s, TBT < 200 ms. Lazy-load modules; edge caching on public content.
Observability	Sentry (errors/perf), Plausible (usage), custom metrics from Mux & Cloudinary via cron ingestion.
Compliance	GDPR export & delete endpoints, data retention 24 months (analytics), monthly access-log audit.
Cost Control	OpenAI usage caps, Plausible free tier, auto-shutdown Preview env after 7 days of inactivity.


⸻

7 · Summary & Next Steps

The HLD maps actors, layers, data flows, and deployment.
Future additions should follow this process:
	1.	New service? → Add to Integration Matrix + Context Diagram.
	2.	Schema change? → Write migration + update data-flow if needed.
	3.	Major new flow? → Add sequence diagram under sequence-diagrams.md.

End of High-Level Design (v2)

