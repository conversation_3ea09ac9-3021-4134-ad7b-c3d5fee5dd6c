# Technology Stack

> **Purpose:** Outline each technology used in Qeleme, explain what it is, why it matters, how we’ll use it, and the benefits for both developers and the AI agent.

---

## Next.js 14 (App Router) + TypeScript

* **What it is:** A React framework with built-in routing, server components, and TypeScript support.
* **Why it matters:** Enables fast development of scalable, SEO-friendly web apps with type safety.
* **Use for:** Building all frontend pages, layouts, and API routes in the Qeleme web application.
* **Benefits:**

  * **Developer:** Combines server-side rendering and static generation for performance, plus strong typing to reduce bugs.
  * **AI Agent:** Can auto-generate route structures, layout components, and typed interfaces.

---

## Tailwind CSS + shadcn/ui

* **What it is:** Utility-first CSS framework (Tailwind) and a ready-made component library (shadcn/ui).
* **Why it matters:** Speeds up UI development with pre-built styles and components, ensuring visual consistency.
* **Use for:** Styling every page and component, and using shadcn/ui for common UI patterns (cards, buttons, forms).
* **Benefits:**

  * **Developer:** Rapid prototyping and consistent design without writing custom CSS.
  * **AI Agent:** Can scaffold styled components quickly using utility classes.

---

## TanStack Query

* **What it is:** A data-fetching library for React that simplifies server state management.
* **Why it matters:** Manages caching, background updates, and synchronization of server data with UI.
* **Use for:** Fetching API and Convex backend data for lessons, user progress, and leaderboards.
* **Benefits:**

  * **Developer:** Reduces boilerplate and handles stale data and refetching automatically.
  * **AI Agent:** Can generate query hooks and cache invalidation logic consistently.

---

## Clerk

* **What it is:** Authentication and user management service with pre-built UI components.
* **Why it matters:** Provides secure, scalable auth flows without custom backend code.
* **Use for:** User sign-in, sign-up, session management, social logins (Google), and OTP flows.
* **Benefits:**

  * **Developer:** Instant auth integration with customizable UI and robust security.
  * **AI Agent:** Can insert protected routes, middleware, and auth wrappers automatically.

---

## Convex 

* **What it is:** Convex provides a real-time database and serverless functions; Prisma is an ORM for type-safe database access.
* **Why it matters:** Simplifies backend logic, real-time updates, and schema management with type safety.
* **Use for:** Storing user profiles, lessons, quizzes, XP, and streak data with real-time syncing.
* **Benefits:**

  * **Developer:** Faster backend development, automatic migrations, and type-checked queries.
  * **AI Agent:** Can generate data model schemas, resolver functions, and migration scripts.

---

## Mux (Video Hosting) + Cloudinary (File Storage)

* **What it is:** Mux for video streaming; Cloudinary for images and PDF hosting.
* **Why it matters:** Ensures efficient media delivery and transformation capabilities.
* **Use for:** Hosting lesson videos on Mux and storing/uploads of images/PDFs on Cloudinary.
* **Benefits:**

  * **Developer:** Pre-built SDKs for media management and responsive delivery.
  * **AI Agent:** Can scaffold upload widgets and video player integration code.

---

## OpenAI GPT-4o (via Serverless Wrappers)

* **What it is:** Advanced language model accessed through custom serverless functions.
* **Why it matters:** Powers AI Tutor Chat, notes generation, and interactive content.
* **Use for:** Generating chat responses, lesson summaries, and quiz questions.
* **Benefits:**

  * **Developer:** Predefined wrappers simplify API calls and error handling.
  * **AI Agent:** Can prototype chat UI and serverless handlers with consistent patterns.

---

## Chopa / Talebirr (Payments - Future)

* **What it is:** Ethiopian payment gateways supporting local currency transactions.
* **Why it matters:** Enables monetization and subscription payments tailored to local users.
* **Use for:** Processing course payments and subscriptions post-MVP.
* **Benefits:**

  * **Developer:** Local integration with familiar payment flows and compliance.
  * **AI Agent:** Can generate payment form components and API integration stubs.

---

## Resend (Email) + Twilio (SMS)

* **What it is:** Resend for transactional emails; Twilio for SMS OTP and notifications.
* **Why it matters:** Reliable delivery of emails and SMS for onboarding and alerts.
* **Use for:** Sending OTP codes, newsletters, and system notifications.
* **Benefits:**

  * **Developer:** Simplified APIs for messaging and high deliverability.
  * **AI Agent:** Can scaffold email templates and SMS verification logic.

---

## Plausible Analytics

* **What it is:** Privacy-friendly web analytics platform.
* **Why it matters:** Tracks user engagement without compromising privacy.
* **Use for:** Monitoring page views, session durations, and feature usage.
* **Benefits:**

  * **Developer:** Lightweight script and simple dashboard for insights.
  * **AI Agent:** Can insert tracking code and metrics events automatically.

---

## Sentry (Error & Performance Monitoring)

* **What it is:** Application monitoring for error tracking and performance metrics.
* **Why it matters:** Detects issues early and measures performance regressions.
* **Use for:** Capturing client and server errors, monitoring API latency.
* **Benefits:**

  * **Developer:** Automated alerts and detailed error context to debug quickly.
  * **AI Agent:** Can wrap code with monitoring calls and set up alert configurations.

---

## Vercel (CI/CD & Deployment)

* **What it is:** Platform for hosting Next.js apps with built-in CI/CD.
* **Why it matters:** Simplifies deployments, previews, and scaling for serverless apps.
* **Use for:** Deploying frontend, serverless functions, and environment management.
* **Benefits:**

  * **Developer:** Zero-config deployments and instant preview URLs.
  * **AI Agent:** Can configure project settings, environment variables, and workflows.

---

## Git & GitHub

* **What it is:** Version control system (Git) and repository hosting (GitHub).
* **Why it matters:** Tracks code history, enables collaboration, and CI integration.
* **Use for:** Managing source code, branches, pull requests, and issues.
* **Benefits:**

  * **Developer:** Robust tooling for code reviews and version management.
  * **AI Agent:** Can create branches, commit changes, and draft pull requests.

---

## Jest & Playwright (Testing)

* **What it is:** Jest for unit/integration tests; Playwright for end-to-end testing.
* **Why it matters:** Ensures application correctness and reliability across features.
* **Use for:** Writing and running automated tests for components, API routes, and UI flows.
* **Benefits:**

  * **Developer:** Quick feedback loop with coverage reports and browser testing.
  * **AI Agent:** Can generate test stubs and maintain test suites over time.

---

## Additional Considerations: Performance & Security

* **Performance Optimizations:** Next.js image and script optimization, lazy-loading, caching strategies.
* **Security Measures:** TLS, input validation (Zod), rate limiting, OWASP best practices, CSP headers.
* **Benefits:**

  * **Developer:** Ensures app is fast and secure by default.
  * **AI Agent:** Can apply security wrappers and performance patterns automatically.
