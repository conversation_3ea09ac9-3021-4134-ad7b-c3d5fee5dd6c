# Taskmaster CLI Cheatsheet 🧩

## Install
# Global
npm install -g task-master-ai

# OR Local (per project)
npm install task-master-ai
npx task-master

## Initialize Project
# Creates tasks/, scripts/, etc.
task-master init
# or (local)
npx task-master init

## Parse PRD
# Reads scripts/prd.txt and generates tasks
task-master parse-prd scripts/prd.txt

## View Tasks
task-master list

## Analyze Complexity
# Scores tasks and suggests breakdowns
task-master complexity-report

## Expand Task into Subtasks
task-master expand --id=<task-id>
# e.g.
task-master expand --id=2

## Get Next Task
task-master next

## Update Task Status
task-master set-status --id=<task-id> --status=in-progress
task-master set-status --id=<task-id> --status=done

## Example Workflow
npx task-master init  
npx task-master parse-prd scripts/prd.txt  
npx task-master list  
npx task-master complexity-report  
npx task-master expand --id=2  
npx task-master next  
npx task-master set-status --id=2.1 --status=done