# Functional & Non-Functional Requirements  
_Qeleme · MVP_

---

## 1. Introduction & Scope
This document specifies **what** Qele<PERSON> must do (Functional Requirements, FR) and **how well** it must perform (Non-Functional Requirements, NFR) for the MVP described in:

- [product-requirements-document.md](./product-requirements-document.md)  
- [minimum-viable-product-features.md](./minimum-viable-product-features.md)

---

## 2. How to Read This Doc
| Field | Meaning | Default |
|-------|---------|---------|
| **ID** | FR-### or NFR-### | Sequential |
| **Priority** | MoSCoW (Must-Have / Should-Have / Could-Have / Won’t-Have) | Must |
| **Status** | Proposed → Approved → In Progress → Done | Proposed |

---

## 3. Functional Requirements

| ID | Title | Description (WHAT) | Priority | Acceptance Criteria | Depends On |
|----|-------|--------------------|----------|---------------------|------------|
| **FR-001** | User Login | Students log in via Google or email/password. | Must | OAuth flow succeeds; invalid credentials show error. | — |
| **FR-002** | OTP Verification | SMS code verification for new accounts. | Must | Correct code logs user in; error on invalid/expired code. | FR-001 |
| **FR-003** | Grade Selection | Capture grade (9 – 12) on first login. | Must | Grade stored in profile and filters content. | FR-001 |
| **FR-004** | Dashboard Greeting | Display personalized greeting + motivational quote. | Should | Greeting shows first name; quote rotates daily. | FR-001 |
| **FR-005** | Progress Bars | Show unit/subject completion % on dashboard. | Must | Bars update after each lesson completion. | FR-015 |
| **FR-006** | Continue Learning | Resume last video at saved timestamp. | Must | Link opens player ≤ 2 s at last position. | FR-015 |
| **FR-007** | Daily Challenge Button | Navigate to daily challenge. | Must | Loads challenge page in ≤ 2 s; streak logic invoked. | FR-020 |
| **FR-008** | Streak Tracker | Count consecutive active days. | Must | Streak increments daily; resets after 24 h gap. | FR-020 |
| **FR-009** | XP Counters | Show daily and total XP. | Must | Counters update in real time after XP events. | FR-014, FR-019, FR-020 |
| **FR-010** | Ad Placeholder | Reserve banner slot on dashboard. | Could | Placeholder renders < 100 ms. | — |
| **FR-011** | AI Chat UI | Real-time chat interface. | Must | Messages stream without page reload. | — |
| **FR-012** | File Upload to AI | Upload PDF/image for AI context. | Should | AI replies reference uploaded content. | FR-011 |
| **FR-013** | AI Answers | GPT-4o returns relevant explanations. | Must | ≥ 80 % user votes “Helpful.” | FR-011 |
| **FR-014** | AI XP Reward | +5 XP per answered AI query. | Must | XP posts immediately after response. | FR-013 |
| **FR-015** | Lesson Navigation | Grade → Subject → Unit → Lesson hierarchy. | Must | Breadcrumb + filters load ≤ 1 s. | — |
| **FR-016** | Video Player | Responsive player, pause/resume. | Must | Buffer ≤ 1 s on seek (3G). | FR-015 |
| **FR-017** | AI Notes | AI-generated lesson summary. | Should | Note appears after 100 % view or on pause. | FR-016 |
| **FR-018** | Embedded Quiz | 3-5 MCQs interrupt video. | Must | Answers stored; UI resumes video after quiz. | FR-016 |
| **FR-019** | Quiz XP | +10 XP × correct answers. | Must | XP update ≤ 2 s after quiz submit. | FR-018 |
| **FR-020** | Daily Challenge Quiz | New quiz every 24 h. | Must | Completing extends streak, awards XP. | — |
| **FR-021** | Profile Stats | Avatar, XP, streak display. | Must | Loads < 1 s; real-time updates. | FR-008, FR-009 |
| **FR-022** | Settings & Logout | Language toggle, logout. | Must | Preference persists; logout clears session. | FR-021 |
| **FR-023** | Bottom Navigation | Home, AI Tutor, Daily Challenge, Profile. | Must | Active icon highlights; nav < 100 ms. | — |
| **FR-024** | Community Placeholder | Banner + beta signup form. | Could | Email stored; banner replaces icon. | — |
| **FR-025** | Pre-Signup Interactive Tutorial Flow | On first “Sign In”/“Get Started” click, show a 3–5 slide animated tutorial before sign-up. | Should | 1. Tutorial starts ≤ 1 s after click.<br>2. Contains 3–5 animated slides.<br>3. Allows skip/fast-forward.<br>4. Routes to sign-up upon finish or skip. | FR-001 |

---

## 4. Non-Functional Requirements

| ID | Category | Description (HOW WELL) | Metric / Threshold | Priority | Rationale |
|----|----------|------------------------|--------------------|----------|-----------|
| **NFR-001** | Performance | Dashboard P90 load time | ≤ 2 s on 3G | Must | Prevent bounce on slow networks |
| **NFR-002** | Performance | Video buffer after seek | ≤ 1 s, P90 | Must | Smooth playback experience |
| **NFR-003** | Scalability | Concurrent users supported | ≥ 10 k | Must | Peak exam-season traffic |
| **NFR-004** | Security | TLS in transit | TLS 1.2+ | Must | Protect credentials |
| **NFR-005** | Security | Role-based access | Only “student” role | Must | Prevent privilege abuse |
| **NFR-006** | Availability | Service uptime | ≥ 99.5 % monthly | Must | Reliable study schedule |
| **NFR-007** | Accessibility | WCAG compliance | AA level | Should | Inclusive design |
| **NFR-008** | Localization | Language support | English & Amharic | Must | Local relevance |
| **NFR-009** | Maintainability | Code quality gate | ESLint + Prettier pass in CI | Should | Fewer regressions |
| **NFR-010** | Observability | Error alert time | < 1 min via Sentry | Should | Rapid debugging |
| **NFR-011** | Tutorial Performance | Tutorial content must load and begin within 1 s of user action. | Should | Ensures smooth, engaging first impression on slow networks. |

---

## 5. Traceability Matrix (Excerpt)

| Requirement | Linked PRD Section | Related MVP Feature |
|-------------|-------------------|---------------------|
| FR-001 - 003 | “Authentication & Onboarding” | Authentication |
| FR-011 - 014 | “AI Tutor Chat” | AI Tutor Page |
| FR-015 - 019 | “Video Lessons” | Video Lesson Module |
| NFR-001 | “Performance” | All pages |

_Full matrix lives in `docs/product-docs/traceability.md` (to-be-generated)._

---

## 6. Change Log
| Date | Author | Change |
|------|--------|--------|
| 2025-06-27 | AI agent | Initial draft |

_Last updated: 2025-06-27_

---