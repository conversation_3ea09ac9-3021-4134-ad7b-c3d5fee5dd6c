# MVP Feature List

> **Purpose:** Outline the core features in the Minimum Viable Product. Each feature includes a description, example user stories, and high-level acceptance criteria. For full technical details, see `FNR.md`.

---

## 1. Authentication

**Description:** Secure, student-only access with multiple sign-in options and initial grade capture.

### Key Capabilities

* Google OAuth sign-in for quick access
* OTP verification via SMS for identity confirmation
* Automatic assignment of “student” role
* Grade selection (9–12) during registration

### Example User Stories

* As a student, I want to sign in with Google so I can use my existing account.
* As a student, I want to verify via OTP so my account stays secure.
* As a new user, I want to select my grade on sign-up so content is personalized.

### Acceptance Criteria

* Users can log in via Google OAuth and email/password.
* OTP codes are sent and validated before granting access.
* All new accounts default to a “student” role with no admin privileges.
* Grade choice is required and stored in the user profile.

---

## 2. Dashboard / Homepage

**Description:** Personalized landing page with progress overview, motivation, and quick access to key actions.

### Key Capabilities

* Welcome greeting with rotating motivational quotes
* Grade selector (persisted across sessions)
* Subject and unit progress display (visual progress bars)
* “Continue Learning” quick link to last lesson
* Daily Challenge entry button
* Streak tracker (consecutive active days)
* XP counter (daily and total)
* Placeholder ad banner for future monetization

### Example User Stories

* As a student, I want to see my daily XP and streak so I feel motivated.
* As a student, I want a “Continue Learning” shortcut so I can pick up where I left off.
* As a student, I want to select my grade from the dashboard so I can switch levels easily.

### Acceptance Criteria

* Dashboard loads in under 2s and displays all key metrics.
* “Continue Learning” link directs to the exact video timestamp paused.
* Daily Challenge button navigates to the challenge page.
* Grade selector updates content scope dynamically.

---

## 3. AI Tutor Page

**Description:** Chat-based interface powered by GPT-4o, supporting file uploads and XP rewards.

### Key Capabilities

* Free-text chat input with AI-generated responses
* Support for PDF, image, and file uploads for context
* Automatic XP reward upon each question answered
* Follow-up prompt suggestions to dive deeper

### Example User Stories

* As a student, I want to upload a PDF so the AI can reference my textbook.
* As a student, I want each valid question to earn XP so I stay engaged.
* As a student, I want follow-up suggestions so I can continue exploring a topic.

### Acceptance Criteria

* Chat UI displays user messages and AI replies in real time.
* Uploaded files are sent to the AI context and responses reflect file content.
* XP is incremented immediately after each successful AI response.
* Follow-up suggestions appear below main answer.

---

## 4. Video Lesson Module

**Description:** Hierarchical lesson navigation with embedded quizzes and AI-generated notes.

### Key Capabilities

* Drill-down from Grade → Subject → Unit → Lesson
* Responsive video player with pause/resume controls
* AI-generated short notes displayed alongside video
* Embedded quiz of 3–5 multiple-choice questions
* XP awarded for quiz completion

### Example User Stories

* As a student, I want to browse lessons by subject so I can find topics easily.
* As a student, I want auto-pausing videos for quizzes so I can test my understanding.
* As a student, I want AI-generated summaries so I can review key points quickly.

### Acceptance Criteria

* Video player supports mobile and desktop layouts.
* Quizzes appear at predefined timestamps and record answers.
* Notes update based on video progress and AI responses.
* XP calculation follows the formula: 10 XP per correct quiz answer.

---

## 5. Daily Challenge

**Description:** One daily quiz-style question or mini-lesson to build habit and streaks.

### Key Capabilities

* Single challenge presented each day
* Multiple-choice or short-answer format
* XP reward for correct completion
* Streak extension on daily participation

### Example User Stories

* As a student, I want to complete a daily challenge so I keep my streak going.
* As a student, I want to earn bonus XP for daily tasks to boost engagement.

### Acceptance Criteria

* Challenge resets every 24 hours at midnight local time.
* XP and streak update immediately on correct answer.
* UI indicates days remaining in current streak.

---

## 6. Profile Page

**Description:** User-centric page showing personal stats, settings, and avatar.

### Key Capabilities

* Display avatar, username, and basic profile details
* Show current streak and total XP earned
* Access to settings: language switcher, logout button

### Example User Stories

* As a student, I want to view my total XP so I can track overall progress.
* As a student, I want to change my language preference for better usability.

### Acceptance Criteria

* Profile data loads in under 1 second.
* Settings persist across sessions.
* Logout successfully clears session and redirects to sign-in.

---

## 7. Navigation

**Description:** Easy access to main sections via a mobile-first bottom navigation bar.

### Key Capabilities

* Persistent bottom nav with icons for: Home, AI Tutor, Daily Challenge, Profile
* Highlight active section with visual indicator
* Smooth transitions between pages

### Example User Stories

* As a student, I want to switch between Home and AI Tutor quickly so I can use the app fluidly.
* As a student, I want to see which page I’m on via the active icon.

### Acceptance Criteria

* Bottom navigation is visible on all primary pages.
* Active icon updates correctly on navigation.
* Navigation performance remains snappy (<100ms transition).

---

## 8. Optional: Community (Coming Soon)

**Description:** Placeholder for future social learning features (forums, study groups).

### Key Capabilities

* Temporary “Coming Soon” banner
* Registration interested users for early access

### Acceptance Criteria

* Banner replaces Community icon until launch.
* Email capture form stores registrations for beta invites.

---
---

## 0. Pre-Signup Interactive Tutorial
**Description:** A brief animated introduction that engages visitors on their first click of “Sign In” or “Get Started,” showcasing core features before account creation.

### Example User Stories
- As a visitor, I want to see a quick tutorial so I understand the app’s value before signing up.  
- As a visitor, I want to select my grade or subject during the tutorial so I feel personally invested.  
- As a visitor, I want to skip the tutorial if I prefer, so I can go directly to sign-up.

### Acceptance Criteria
- Tutorial launches within 1 s of the initial “Sign In”/“Get Started” click.  
- Consists of **3–5 animated slides** covering key benefits.  
- User can **skip or fast-forward** at any time.  
- After completion or skip, navigates to the sign-up form seamlessly.

*End of MVP Feature List*
