# Product Requirements Document (PRD)

> **Purpose:** This PRD captures the vision, goals, target users, and prioritized features for Qeleme’s MVP. It will serve as our single source of truth during development.

## 1. Vision & Problem Statement

* **Vision:** Empower Ethiopian high-school students (Grades 9–12) with AI-powered, interactive learning tools that boost engagement and mastery.
* **Problem:** Traditional study methods lack personalization, real-time feedback, and gamified motivation. Students often struggle to stay consistent and do not receive immediate help.

## 2. Target Audience & Personas

1. **High-performing Student**: Age 15–18, academically motivated, seeks challenge and deeper understanding.
2. **Struggling Student**: Age 15–18, needs structured guidance and motivation to study regularly.
3. **Busy Student**: Age 15–18, balancing studies with work or family responsibilities, needs flexible, bite-size learning.

## 3. Objectives & Success Metrics

| Objective                                   | Metric                                             |
| ------------------------------------------- | -------------------------------------------------- |
| Increase study consistency                  | 50% of active users complete 5+ sessions/week      |
| Improve learning outcomes                   | 70% of users improve quiz scores by 20% in 1 month |
| Boost engagement through gamification       | 60% of users earn XP daily                         |
| Enhance satisfaction with personalized help | 85% positive feedback rating                       |

## 4. MVP Feature List (Priority)
1. User Authentication & Onboarding  
2. Video Lessons  
3. Lesson Player  
4. AI Tutor Chat  
5. Gamification Engine  
6. Progress Dashboard  

_For full descriptions and acceptance criteria, see [MVP-Features.md](./MVP-Features.md)._

## 5. Requirements Overview
- Functional & Non-Functional requirements live in [FNR.md](./FNR.md).

## 7. Constraints & Assumptions

* **Constraints:** Limited internet bandwidth; smartphone-first design.
* **Assumptions:** Users have basic smartphone literacy; Convex DB can handle real-time data.

## 8. Timeline & Milestones

* **Weeks 1–2:** Foundation setup & authentication.
* **Weeks 3–4:** Lesson player & quizzes.
* **Weeks 5–6:** AI tutor integration.
* **Weeks 7–8:** Gamification & dashboard.
* **Week 9:** QA, bug fixing, launch.

---

*End of PRD*
