# Development Phases Roadmap

> **Purpose:** Outline the phases of development for the Qeleme MVP, and define the folder and file structure for each phase.

---

## What is a Development Phase?
A **Development Phase** is a focused, time-boxed slice of work that delivers a cohesive set of functionality. Each phase follows a consistent template to guide the AI agent (and human developers) through planning, implementation, and review.

### Phase Folder Structure
For each phase, create a folder under \`docs/roadmap/phases/\` named \`NN-phase-name\`. Inside, include:

| File              | Purpose                                                                  |
|-------------------|--------------------------------------------------------------------------|
| \`01-overview.md\`     | High-level objectives, success criteria, and a mini architecture diagram. |
| \`02-features.md\`     | Detailed list of features or user stories derived from the overview.      |
| \`03-error-log.md\`    | Log of errors encountered during implementation and how they were fixed.   |
| \`04-ai-actions.md\`   | Record of actions the AI agent took: file creations, code generations, fixes. |

---

## Phase List
| Phase | Name                  | Goal                                             | Folder                         |
|------:|-----------------------|--------------------------------------------------|--------------------------------|
| 1     | Setup                 | Initialize project skeleton & tech stack         | \`phases/01-setup\`            |
| 2     | Authentication        | Implement OAuth, SMS/OTP signup, grade capture   | \`phases/02-authentication\`   |
| 3     | Video Lessons         | Build lesson browsing + video player + quizzes   | \`phases/03-video-lessons\`    |
| 4     | AI Tutor              | Create chat UI, file upload, GPT-4o integration  | \`phases/04-ai-tutor\`         |
| 5     | Daily Challenge       | One-per-day quiz with XP rewards & streaks       | \`phases/05-daily-challenge\`  |
| 6     | Profile & Navigation  | User profile page + persistent bottom nav        | \`phases/06-profile-navigation\`|
