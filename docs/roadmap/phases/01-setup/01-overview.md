# Phase 01 – Setup Overview

> **Purpose:** Establish the foundational skeleton and configure all core technologies so we can build features on a solid, consistent base.

---

## 1. High-Level Objectives
- **Initialize Next.js 14 App** with TypeScript & App Router
- **Configure Tailwind CSS** + shadcn/ui for styling
- **Install & Configure Core SDKs:** Clerk, Convex, OpenAI, Twilio, Resend
- **Add Developer Tooling:** ESLint, Prettier, <PERSON>sky + lint-staged
- **Set Up Testing Frameworks:** Jest for unit tests, Playwright for E2E
- **Environment Configuration:** Create \`.env.local.example\` with all required variables
- **Basic CI/CD Pipeline:** Ensure Vercel preview deploy works and runs migrations/tests

---

## 2. Success Criteria
1. Running \`npm run dev\` launches a blank Next.js page on \`http://localhost:3000\`
2. Tailwind styles applied (e.g., sample colored header)
3. \`ClerkProvider\` and \`ConvexProvider\` wrap the app without errors
4. Environment variables load correctly; missing keys throw clear errors
5. \`npm test\` runs and passes (zero Jest failures)
6. <PERSON><PERSON> can navigate to the home page in \`npm run e2e\`
7. Vercel preview build succeeds in CI, migrations apply, and preview URL is live

---

## 3. Mini Architecture Diagram
\`\`\`mermaid
flowchart TB
  subgraph Dev Environment
    Next[Next.js 14 App]
    Tailwind[Tailwind CSS + shadcn/ui]
    ESLint[ESLint + Prettier]
    Jest[Jest]
    Playwright[Playwright]
  end

  subgraph Providers
    Clerk[Clerk Provider]
    Convex[Convex Client]
    OpenAI[OpenAI Wrapper]
    Twilio[Twilio SDK]
    Resend[Resend SDK]
  end

  Next --> Tailwind
  Next --> ESLint
  Next --> Jest
  Next --> Playwright
  Next --> Clerk
  Next --> Convex
  Next --> OpenAI
  Next --> Twilio
  Next --> Resend
\`\`\`

---

## 4. Next Steps
1. **Fill out \`02-features.md\`** for Phase 01 with specific setup tasks  
2. Document errors in \`03-error-log.md\` as you encounter configuration issues  
3. Record AI actions in \`04-ai-actions.md\` when using the agent to scaffold or configure  
4. Confirm success criteria and mark Phase 01 complete before moving on
