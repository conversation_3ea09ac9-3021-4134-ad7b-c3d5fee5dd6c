# Phase 01 – Setup Features

> **Purpose:** Break down the setup objectives into detailed, actionable steps for the AI agent to implement.

---

## 1. Next.js 14 App Initialization
- **Command:**
  \`\`\`bash
  npx create-next-app@latest . \\
    --typescript \\
    --app \\
    --src-dir \\
    --import-alias "@/*" \\
    --use-npm
  \`\`\`
- **Steps:**
  1. Confirm project folder contains \`app/\`, \`public/\`, \`package.json\`.
  2. Open in VS Code and verify TypeScript support.

## 2. Tailwind CSS & shadcn/ui Setup
- **Commands:**
  \`\`\`bash
  npm install -D tailwindcss postcss autoprefixer
  npx tailwindcss init -p
  npm install shadcn/ui
  \`\`\`
- **Configuration:**
  1. Edit \`tailwind.config.js\`.
  2. Add \`@tailwind base/components/utilities\` in \`globals.css\`.
  3. Run \`npx shadcn-ui init\`.

## 3. Core SDKs Installation & Configuration
- **Clerk:** \`npm install @clerk/nextjs\`, wrap \`<ClerkProvider>\`, add middleware, update \`.env.local\`.
- **Convex:** \`npm install convex@latest\`, run \`npx convex init\`, create \`src/lib/convex.ts\`, wrap in \`<ConvexProvider>\`.
- **OpenAI GPT-4o:** \`npm install openai\`, add \`OPENAI_API_KEY\`, create \`lib/openai.ts\`.
- **Twilio & Resend:** \`npm install twilio @resend/client\`, configure in \`lib/twilio.ts\` and \`lib/email.ts\`.

## 4. Developer Tooling
- **ESLint & Prettier:** \`npm install -D eslint prettier eslint-config-prettier eslint-plugin-prettier\`, run \`npx eslint --init\`.
- **Husky & lint-staged:** \`npm install -D husky lint-staged\`, run \`npx husky install\`, configure \`lint-staged\` in \`package.json\`.

## 5. Testing Setup
- **Jest:** \`npm install -D jest @types/jest ts-jest\`, run \`npx ts-jest config:init\`.
- **Playwright:** \`npm install -D @playwright/test\`, run \`npx playwright install\`.

## 6. Environment & CI/CD
- **Env Example:** \`cp .env.local.example .env.local\`
- **CI Workflow:** Ensure GitHub Actions runs \`npm test\` & \`npx convex db migrate\`. Confirm Vercel preview deploys.

---

> After completing these detailed setup tasks, record any errors in \`03-error-log.md\` and log AI steps in \`04-ai-actions.md\` before marking Phase 01 complete.
