# Blueprint for Thinking Paradigms

> **This document is for the AI agent.**  
> Use it to guide your thinking and automatically generate a structured thinking file for every feature before writing any code.

## 1. Purpose & How to Use
1. **When:** Immediately upon receiving a request to implement a new feature.  
2. **What to do:**  
   - Read this entire blueprint.  
   - Create a file at `docs/thinking-paradigms/<feature>-thinking-paradigm.md` (use lowercase, hyphens).  
   - Fill in each section (Logical, Analytical, Computational, Procedural Thinking) with precise questions, decisions, and flows.  
3. **Next steps:** After completing and saving the thinking-paradigm file, signal readiness and then begin coding the feature.

---

## 2. The Four Thinking Models
Use these headings exactly. For each feature, answer the **Key Question** and record **Decision / Notes**.

### 2.1 Logical Thinking
- **Definition:** Use clear “if…then…” rules to check that every scenario makes sense.  
- **Example:** If the user is not authenticated, then redirect to the sign-in page.  
- **Analogy:** Like a recipe: skipping an ingredient changes the final dish.

### 2.2 Analytical Thinking
- **Definition:** Break the feature into parts to identify all components and dependencies.  
- **Example:** Sign-In = UI form + API call + error handling + state management.  
- **Analogy:** Like dismantling a bicycle to see how wheels, chain, and pedals connect.

### 2.3 Computational Thinking
- **Definition:** Define inputs, processes, and outputs so code can execute each step.  
- **Example:** Input: email/password → Process: call auth API → Output: success or error flag.  
- **Analogy:** Giving exact instructions to a robot to avoid misunderstandings.

### 2.4 Procedural Thinking
- **Definition:** Outline the exact sequence of steps to complete the feature.  
- **Example:** 1) Validate form → 2) Show loading → 3) Send request → 4) Handle response → 5) Navigate or show error.  
- **Analogy:** Like following IKEA instructions: step order matters.

---

## 3. Feature-Thinking Template
Copy the code block below into a new file named `<feature>-thinking-paradigm.md` and replace placeholders:

```md
# Feature: <FEATURE NAME>

## Logical Thinking
- **Key Question:** <what conditional rule checks must run?>
- **Decision / Notes:** <if X, then Y>

## Analytical Thinking
- **Key Question:** <what parts and dependencies exist?>
- **Decision / Notes:**
  - Part 1: …
  - Part 2: …

## Computational Thinking
- **Key Question:** <how does input become output?>
- **Decision / Notes:**
  1. Read input: …
  2. Process: …
  3. Output: …

## Procedural Thinking
- **Key Question:** <what is the exact flow order?>
- **Step-by-Step Flow:**
  1. …
  2. …
  3. …

  Action: After generating the file, confirm completion and await instruction before coding.

  4. Worked Example: Sign-In

(Use this as a reference; do not modify this file.)

# Feature: Sign-In

## Logical Thinking
- **Key Question:** What happens if an unauthenticated user tries to access the dashboard?
- **Decision / Notes:** Redirect to `/sign-in`.

## Analytical Thinking
- **Key Question:** Which components and services are involved?
- **Decision / Notes:**
  - UI: form, buttons, error messages
  - API: Clerk authentication endpoint
  - State: loading, success, error flags

## Computational Thinking
- **Key Question:** How is the sign-in request processed?
- **Decision / Notes:**
  1. Capture form values
  2. Send JSON payload to auth API
  3. Await response
  4. Store session or display error

## Procedural Thinking
- **Key Question:** What is the complete step order?
- **Step-by-Step Flow:**
  1. User enters credentials
  2. Clicks “Sign In”
  3. Validate inputs
  4. Display loading state
  5. Call auth API
  6. Handle response (redirect or error)

  5. AI Agent Instructions
	•	Always create a thinking-paradigm file for each new feature.
	•	Do not write code until the thinking-paradigm file is complete.
	•	Use the exact headings and formatting above for consistency.
	•	After creating and filling the file, notify the user: “Thinking paradigm for <feature> complete.”

  