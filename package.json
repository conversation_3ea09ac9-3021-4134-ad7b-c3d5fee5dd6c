{"name": "qeleme-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^5.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "convex": "^1.0.0", "lucide-react": "^0.400.0", "next": "^15.0.0", "openai": "^4.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.0", "task-master-ai": "^0.18.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "postcss": "^8.0.0", "tailwindcss": "^3.0.0", "typescript": "^5.0.0"}}