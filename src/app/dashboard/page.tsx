import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Navigation } from "@/components/navigation";

export default function DashboardPage() {
  const { userId } = auth();

  if (!userId) {
    redirect("/sign-in");
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Navigation />

      <div className="container mx-auto p-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Your Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Continue your learning journey.</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-6 border rounded-lg shadow-sm bg-card">
            <h2 className="text-xl font-semibold mb-4">Continue Learning</h2>
            <p className="text-muted-foreground mb-4">You have no active courses yet.</p>
            <Link href="/lessons">
              <button className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90">
                Browse Lessons
              </button>
            </Link>
          </div>

          <div className="p-6 border rounded-lg shadow-sm bg-card">
            <h2 className="text-xl font-semibold mb-4">AI Tutor</h2>
            <p className="text-muted-foreground mb-4">Ask questions and get help with your studies.</p>
            <Link href="/ai-tutor">
              <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                Start Chat
              </button>
            </Link>
          </div>

          <div className="p-6 border rounded-lg shadow-sm bg-card">
            <h2 className="text-xl font-semibold mb-4">Daily Challenge</h2>
            <p className="text-muted-foreground mb-4">Complete today's challenge to earn XP.</p>
            <button className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
              Start Challenge
            </button>
          </div>

          <div className="p-6 border rounded-lg shadow-sm bg-card">
            <h2 className="text-xl font-semibold mb-4">Your Progress</h2>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">XP Total</span>
                <span className="font-semibold">0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Lessons Completed</span>
                <span className="font-semibold">0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Current Streak</span>
                <span className="font-semibold">0 days</span>
              </div>
            </div>
          </div>

          <div className="p-6 border rounded-lg shadow-sm bg-card">
            <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
            <div className="space-y-2">
              <Link href="/lessons" className="block">
                <button className="w-full text-left px-3 py-2 rounded hover:bg-accent">
                  📚 Browse Lessons
                </button>
              </Link>
              <Link href="/ai-tutor" className="block">
                <button className="w-full text-left px-3 py-2 rounded hover:bg-accent">
                  🤖 Ask AI Tutor
                </button>
              </Link>
              <Link href="/profile" className="block">
                <button className="w-full text-left px-3 py-2 rounded hover:bg-accent">
                  👤 View Profile
                </button>
              </Link>
            </div>
          </div>

          <div className="p-6 border rounded-lg shadow-sm bg-card">
            <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
            <p className="text-muted-foreground text-sm">No recent activity yet. Start learning to see your progress here!</p>
          </div>
        </div>
      </div>
    </div>
  );
}