import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { ConvexClientProvider } from '@/lib/convex';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'Qeleme - AI Learning Platform',
  description: 'AI-powered learning for Ethiopian high-school students',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ClerkProvider>
          <ConvexClientProvider>
            {children}
          </ConvexClientProvider>
        </ClerkProvider>
      </body>
    </html>
  );
}