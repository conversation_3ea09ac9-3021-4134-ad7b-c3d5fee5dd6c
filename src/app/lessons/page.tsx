import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { Navigation } from "@/components/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function LessonsPage() {
  const { userId } = auth();
  
  if (!userId) {
    redirect("/sign-in");
  }
  
  return (
    <div className="flex flex-col min-h-screen">
      <Navigation />
      
      <div className="container mx-auto p-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Lessons</h1>
          <p className="text-muted-foreground">Browse and access video lessons for your grade level.</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Grade 9</CardTitle>
              <CardDescription>Foundation courses for Grade 9 students</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Mathematics</div>
                <div className="text-sm text-muted-foreground">Physics</div>
                <div className="text-sm text-muted-foreground">Chemistry</div>
                <div className="text-sm text-muted-foreground">Biology</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Grade 10</CardTitle>
              <CardDescription>Advanced courses for Grade 10 students</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Mathematics</div>
                <div className="text-sm text-muted-foreground">Physics</div>
                <div className="text-sm text-muted-foreground">Chemistry</div>
                <div className="text-sm text-muted-foreground">Biology</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Grade 11</CardTitle>
              <CardDescription>Preparation for university entrance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Mathematics</div>
                <div className="text-sm text-muted-foreground">Physics</div>
                <div className="text-sm text-muted-foreground">Chemistry</div>
                <div className="text-sm text-muted-foreground">Biology</div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Grade 12</CardTitle>
              <CardDescription>Final year preparation and review</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Mathematics</div>
                <div className="text-sm text-muted-foreground">Physics</div>
                <div className="text-sm text-muted-foreground">Chemistry</div>
                <div className="text-sm text-muted-foreground">Biology</div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="mt-8 text-center">
          <p className="text-muted-foreground">More lessons coming soon! We're working hard to add more content.</p>
        </div>
      </div>
    </div>
  );
}
