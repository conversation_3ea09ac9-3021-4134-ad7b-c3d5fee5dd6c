import { SignUpButton, SignedIn, SignedOut } from "@clerk/nextjs";
import Link from "next/link";
import { Navigation } from "@/components/navigation";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      <Navigation />

      <main className="flex-1 flex flex-col items-center justify-center p-8 text-center">
        <h2 className="text-4xl font-bold mb-6">
          AI-Powered Learning for Ethiopian High School Students
        </h2>
        <p className="text-xl max-w-2xl mb-8">
          Access video lessons, practice with quizzes, and get help from our AI tutor
          to excel in your studies.
        </p>
        <SignedOut>
          <SignUpButton mode="modal">
            <button className="px-6 py-3 rounded-lg bg-blue-600 text-white text-lg hover:bg-blue-700">
              Get Started
            </button>
          </SignUpButton>
        </SignedOut>
        <SignedIn>
          <Link href="/dashboard">
            <button className="px-6 py-3 rounded-lg bg-blue-600 text-white text-lg hover:bg-blue-700">
              Go to Dashboard
            </button>
          </Link>
        </SignedIn>
      </main>

      <footer className="p-4 border-t text-center text-gray-500">
        © {new Date().getFullYear()} Qeleme. All rights reserved.
      </footer>
    </div>
  );
}