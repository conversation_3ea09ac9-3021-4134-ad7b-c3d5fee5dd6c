import Link from "next/link";
import { SignedIn, Signed<PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, SignIn<PERSON>utton, SignUpButton } from "@clerk/nextjs";

export function Navigation() {
  return (
    <nav className="bg-background border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold text-primary">
            Qeleme
          </Link>

          <div className="flex items-center gap-6">
            <SignedIn>
              <div className="flex items-center gap-4">
                <Link
                  href="/dashboard"
                  className="text-foreground hover:text-primary transition-colors"
                >
                  Dashboard
                </Link>
                <Link
                  href="/lessons"
                  className="text-foreground hover:text-primary transition-colors"
                >
                  Lessons
                </Link>
                <Link
                  href="/ai-tutor"
                  className="text-foreground hover:text-primary transition-colors"
                >
                  AI Tutor
                </Link>
                <UserButton
                  afterSignOutUrl="/"
                  appearance={{
                    elements: {
                      avatarBox: "w-8 h-8"
                    }
                  }}
                />
              </div>
            </SignedIn>

            <SignedOut>
              <div className="flex gap-4">
                <SignInButton mode="modal">
                  <button className="px-4 py-2 rounded border border-border hover:bg-accent transition-colors">
                    Sign In
                  </button>
                </SignInButton>
                <SignUpButton mode="modal">
                  <button className="px-4 py-2 rounded bg-primary text-primary-foreground hover:bg-primary/90 transition-colors">
                    Sign Up
                  </button>
                </SignUpButton>
              </div>
            </SignedOut>
          </div>
        </div>
      </div>
    </nav>
  );
}
