import OpenAI from 'openai';

// Initialize the OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export default openai;

// Helper function for chat completions
export async function createChatCompletion(messages: any[], options = {}) {
  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages,
      temperature: 0.7,
      max_tokens: 1000,
      ...options,
    });
    
    return response;
  } catch (error) {
    console.error('OpenAI API error:', error);
    throw error;
  }
}